# frozen_string_literal: true

module SubscriptionHelper
  # Map Stripe plan ID to user-friendly name
  def plan_display_name(plan_id)
    subscription_manager = SubscriptionManager.new(Current.user)
    plan_info = subscription_manager.plan_info(plan_id)
    plan_info&.dig(:name) || 'Unknown Plan'
  end

  # Get plan description
  def plan_description(plan_id)
    subscription_manager = SubscriptionManager.new(Current.user)
    plan_info = subscription_manager.plan_info(plan_id)
    plan_info&.dig(:description) || 'Subscription plan'
  end

  # Get plan price
  def plan_price(plan_id)
    subscription_manager = SubscriptionManager.new(Current.user)
    plan_info = subscription_manager.plan_info(plan_id)
    plan_info&.dig(:price) || 'Contact us'
  end

  # Get plan features
  def plan_features(plan_id)
    subscription_manager = SubscriptionManager.new(Current.user)
    plan_info = subscription_manager.plan_info(plan_id)
    plan_info&.dig(:features) || []
  end

  # Check if user can upgrade to a specific plan
  def can_upgrade_to_plan?(plan_id)
    return false unless Current.user
    subscription_manager = SubscriptionManager.new(Current.user)
    subscription_manager.can_upgrade_to?(plan_id)
  end

  # Check if user can downgrade to a specific plan
  def can_downgrade_to_plan?(plan_id)
    return false unless Current.user
    subscription_manager = SubscriptionManager.new(Current.user)
    subscription_manager.can_downgrade_to?(plan_id)
  end

  # Get subscription status badge class
  def subscription_status_badge_class(subscription)
    case subscription&.status
    when 'active'
      'bg-green-100 text-green-800'
    when 'trialing'
      'bg-blue-100 text-blue-800'
    when 'past_due'
      'bg-yellow-100 text-yellow-800'
    when 'canceled', 'cancelled'
      'bg-red-100 text-red-800'
    when 'incomplete'
      'bg-gray-100 text-gray-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  # Get billing cycle information
  def plan_billing_cycle(plan_id)
    PLAN_MAPPINGS.dig(plan_id, :billing_cycle) || 'Monthly'
  end

  # Get plan features
  def plan_features(plan_id)
    PLAN_MAPPINGS.dig(plan_id, :features) || []
  end

  # Check if plan is premium
  def premium_plan?(plan_id)
    plan_id == SubscriptionManager::PREMIUM_PLAN_ID
  end

  # Check if plan is standard
  def standard_plan?(plan_id)
    plan_id == SubscriptionManager::STANDARD_PLAN_ID
  end

  # Get subscription status badge classes
  def subscription_status_badge_classes(status)
    case status.to_s.downcase
    when "active"
      # Most prominent - darker stone background with white text for active status
      "inline-flex items-center rounded-md bg-stone-800 px-2 py-1 text-xs font-medium text-white ring-1 ring-inset ring-stone-700/20"
    when "trialing"
      # Secondary prominence - medium stone background for trial period
      "inline-flex items-center rounded-md bg-stone-200 px-2 py-1 text-xs font-medium text-stone-800 ring-1 ring-inset ring-stone-600/20"
    when "past_due"
      # Warning state - lighter stone with darker text for attention
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    when "canceled", "cancelled"
      # Muted state - very light stone for cancelled subscriptions
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    when "incomplete"
      # Attention needed - medium stone background
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    when "incomplete_expired"
      # Expired state - lightest stone background
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    when "unpaid"
      # Problem state - light stone with darker text for visibility
      "inline-flex items-center rounded-md bg-stone-100 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-600/20"
    else
      # Default fallback - neutral stone styling
      "inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-600 ring-1 ring-inset ring-stone-500/20"
    end
  end

  # Get subscription status display text
  def subscription_status_display(status)
    case status.to_s.downcase
    when "active"
      "Active"
    when "trialing"
      "Trial Period"
    when "past_due"
      "Past Due"
    when "canceled", "cancelled"
      "Cancelled"
    when "incomplete"
      "Incomplete"
    when "incomplete_expired"
      "Expired"
    when "unpaid"
      "Unpaid"
    else
      status.to_s.humanize
    end
  end

  # Format next billing date
  def format_billing_date(date)
    return 'Not available' unless date.present?
    
    if date.respond_to?(:strftime)
      date.strftime('%B %d, %Y')
    else
      date.to_s
    end
  end

  # Get subscription card classes for different plan types
  def subscription_card_classes(plan_id)
    base_classes = 'rounded-lg border p-6 shadow-sm'
    
    if premium_plan?(plan_id)
      "#{base_classes} border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50"
    elsif standard_plan?(plan_id)
      "#{base_classes} border-stone-200 bg-stone-50"
    else
      "#{base_classes} border-stone-200 bg-white"
    end
  end

  # Get plan badge classes
  def plan_badge_classes(plan_id)
    if premium_plan?(plan_id)
      'inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10'
    elsif standard_plan?(plan_id)
      'inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-700/10'
    else
      'inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-700/10'
    end
  end
end
