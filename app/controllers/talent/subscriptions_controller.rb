# frozen_string_literal: true

module Talent
  class SubscriptionsController < Talent::BaseController
    # Ensure user is authenticated if BaseController doesn't handle it
    # before_action :authenticate_user!

    def create
      plan_id = params[:plan] # Get the Price ID from params

      # Validate plan_id presence (optional but recommended)
      unless plan_id.present?
        redirect_to talent_settings_subscription_path, alert: 'Subscription plan not specified.' # Redirect back to settings
        return
      end

      begin
        # Check if user already has an active subscription
        existing_subscription = Current.user.subscriptions.active.first

        if existing_subscription
          # Handle subscription upgrade/downgrade
          handle_subscription_change(existing_subscription, plan_id)
        else
          # Create new subscription
          create_new_subscription(plan_id)
        end

      rescue Pay::Error => e
        redirect_to cancel_talent_subscription_url, alert: "Subscription failed: #{e.message}" # Redirect to cancel URL on Pay::Error
      rescue StandardError => e
        # Catch other potential errors
        Rails.logger.error("Subscription Error: #{e.message}\n#{e.backtrace.join("\n")}")
        redirect_to cancel_talent_subscription_url, alert: 'An unexpected error occurred during subscription.' # Redirect to cancel URL
      end
    end

    def success
      # Optionally fetch subscription details or just show a success message
      flash[:notice] = 'Subscription successful!'
      # Redirect to settings page or dashboard after showing success
      redirect_to talent_settings_subscription_path, notice: 'Subscription activated successfully!'
    end

    def cancel
      # Show a cancellation message
      flash[:alert] = 'Subscription process was cancelled.'
      # Redirect back to the settings page
      redirect_to talent_settings_subscription_path, alert: 'Subscription process cancelled.'
    end

    def upgrade
      plan_id = params[:plan]

      unless plan_id.present?
        redirect_to talent_settings_subscription_path, alert: 'Subscription plan not specified.'
        return
      end

      existing_subscription = Current.user.subscriptions.active.first

      unless existing_subscription
        redirect_to talent_settings_subscription_path, alert: 'No active subscription found to upgrade.'
        return
      end

      begin
        handle_subscription_change(existing_subscription, plan_id)
      rescue Pay::Error => e
        redirect_to talent_settings_subscription_path, alert: "Subscription upgrade failed: #{e.message}"
      rescue StandardError => e
        Rails.logger.error("Subscription Upgrade Error: #{e.message}\n#{e.backtrace.join("\n")}")
        redirect_to talent_settings_subscription_path, alert: 'An unexpected error occurred during subscription upgrade.'
      end
    end

    # Optional: Add destroy action if you allow cancellation via your app
    # def destroy
    #   subscription = Current.user.subscription
    #   if subscription
    #     begin
    #       subscription.cancel_now! # Or .cancel to cancel at period end
    #       redirect_to talent_dashboard_path, notice: 'Subscription cancelled successfully.'
    #     rescue Pay::Error => e
    #       redirect_to talent_dashboard_path, alert: "Cancellation failed: #{e.message}"
    #     end
    #   else
    #     redirect_to talent_dashboard_path, alert: 'No active subscription found to cancel.'
    #   end
    # end

    private

    def handle_subscription_change(existing_subscription, new_plan_id)
      subscription_manager = SubscriptionManager.new(Current.user)

      # Use the service to handle the subscription change
      subscription_manager.change_subscription(new_plan_id)

      # The webhook will handle updating the Pay::Subscription record and user premium status
      redirect_to talent_settings_subscription_path, notice: 'Subscription updated successfully! Changes will be reflected shortly.'
    end

    def create_new_subscription(plan_id)
      subscription_manager = SubscriptionManager.new(Current.user)

      # Use the service to create checkout session
      checkout_session = subscription_manager.create_subscription_checkout(
        plan_id,
        success_talent_subscription_url,
        cancel_talent_subscription_url
      )

      # Redirect to Stripe Checkout page
      redirect_to checkout_session.url, allow_other_host: true, status: :see_other
    end

    # Define talent_dashboard_path if not already available globally
    # def talent_dashboard_path
    #   # Your path helper for the talent dashboard
    # end
    # def talent_dashboard_url(options = {})
    #   # Your url helper for the talent dashboard
    # end
  end
end
