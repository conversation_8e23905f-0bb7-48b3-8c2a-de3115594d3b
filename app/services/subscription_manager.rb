# frozen_string_literal: true

class SubscriptionManager
  include ActiveModel::Model
  include ActiveModel::Attributes

  STANDARD_PLAN_ID = 'price_1R9Q55DYYVPVcCCrWQOwsKmT'
  PREMIUM_PLAN_ID = 'price_1R9Q66DYYVPVcCCrnqiXNafF'

  PLAN_HIERARCHY = {
    STANDARD_PLAN_ID => 1,
    PREMIUM_PLAN_ID => 2
  }.freeze

  attr_accessor :user

  def initialize(user)
    @user = user
  end

  # Check if user can have only one active subscription
  def ensure_single_subscription!
    active_subscriptions = user.subscriptions.active
    
    if active_subscriptions.count > 1
      Rails.logger.warn "User #{user.id} has multiple active subscriptions: #{active_subscriptions.pluck(:id)}"
      
      # Keep the most recent subscription and cancel others
      latest_subscription = active_subscriptions.order(created_at: :desc).first
      subscriptions_to_cancel = active_subscriptions.where.not(id: latest_subscription.id)
      
      subscriptions_to_cancel.each do |subscription|
        begin
          subscription.cancel_now!
          Rails.logger.info "Cancelled duplicate subscription #{subscription.id} for user #{user.id}"
        rescue => e
          Rails.logger.error "Failed to cancel duplicate subscription #{subscription.id}: #{e.message}"
        end
      end
    end
  end

  # Get current active subscription
  def current_subscription
    @current_subscription ||= user.subscriptions.active.first
  end

  # Check if user has any active subscription
  def has_active_subscription?
    current_subscription.present?
  end

  # Check if user has standard plan
  def has_standard_plan?
    current_subscription&.processor_plan == STANDARD_PLAN_ID
  end

  # Check if user has premium plan
  def has_premium_plan?
    current_subscription&.processor_plan == PREMIUM_PLAN_ID
  end

  # Get current plan level (0 = none, 1 = standard, 2 = premium)
  def current_plan_level
    return 0 unless current_subscription
    PLAN_HIERARCHY[current_subscription.processor_plan] || 0
  end

  # Check if upgrade is possible
  def can_upgrade_to?(target_plan_id)
    return false unless has_active_subscription?
    return false if current_subscription.processor_plan == target_plan_id
    
    current_level = current_plan_level
    target_level = PLAN_HIERARCHY[target_plan_id] || 0
    
    target_level > current_level
  end

  # Check if downgrade is possible
  def can_downgrade_to?(target_plan_id)
    return false unless has_active_subscription?
    return false if current_subscription.processor_plan == target_plan_id
    
    current_level = current_plan_level
    target_level = PLAN_HIERARCHY[target_plan_id] || 0
    
    target_level < current_level && target_level > 0
  end

  # Perform subscription change with proration
  def change_subscription(new_plan_id)
    raise ArgumentError, "Invalid plan ID" unless valid_plan_id?(new_plan_id)
    raise StandardError, "No active subscription found" unless current_subscription
    raise StandardError, "Already subscribed to this plan" if current_subscription.processor_plan == new_plan_id

    # Ensure user has Stripe processor
    user.set_payment_processor(:stripe)

    # Get current Stripe subscription
    stripe_subscription = Stripe::Subscription.retrieve(current_subscription.processor_id)
    
    # Determine proration behavior based on upgrade/downgrade
    proration_behavior = determine_proration_behavior(new_plan_id)
    
    # Update subscription with proration
    updated_subscription = Stripe::Subscription.update(
      current_subscription.processor_id,
      {
        items: [
          {
            id: stripe_subscription.items.data[0].id,
            price: new_plan_id,
          }
        ],
        proration_behavior: proration_behavior,
        billing_cycle_anchor: 'unchanged'
      }
    )

    Rails.logger.info "Subscription changed for user #{user.id}: #{current_subscription.processor_plan} -> #{new_plan_id}"
    
    # Return the updated subscription
    updated_subscription
  end

  # Create new subscription via Stripe Checkout
  def create_subscription_checkout(plan_id, success_url, cancel_url)
    raise ArgumentError, "Invalid plan ID" unless valid_plan_id?(plan_id)
    raise StandardError, "User already has an active subscription" if has_active_subscription?

    # Ensure user has Stripe processor
    user.set_payment_processor(:stripe)

    # Create checkout session
    checkout_session = user.payment_processor.checkout(
      mode: 'subscription',
      line_items: [{ price: plan_id, quantity: 1 }],
      success_url: success_url,
      cancel_url: cancel_url,
      subscription_data: {
        metadata: {
          user_id: user.id,
          plan_type: plan_type_from_id(plan_id)
        }
      }
    )

    checkout_session
  end

  # Get plan display information
  def plan_info(plan_id = nil)
    plan_id ||= current_subscription&.processor_plan
    return nil unless plan_id

    case plan_id
    when STANDARD_PLAN_ID
      {
        id: plan_id,
        name: 'Standard',
        description: 'Standard subscription plan',
        price: '$29.00',
        billing_cycle: 'Monthly',
        level: 1,
        features: ['Basic features', 'Standard support']
      }
    when PREMIUM_PLAN_ID
      {
        id: plan_id,
        name: 'Premium',
        description: 'Premium subscription plan with advanced features',
        price: '$99.00',
        billing_cycle: 'Monthly',
        level: 2,
        features: ['All Standard features', 'Premium support', 'Advanced analytics', 'Priority listing']
      }
    else
      nil
    end
  end

  # Get available upgrade options
  def available_upgrades
    return [] unless has_active_subscription?
    
    current_level = current_plan_level
    available_plans = []
    
    PLAN_HIERARCHY.each do |plan_id, level|
      if level > current_level
        available_plans << plan_info(plan_id)
      end
    end
    
    available_plans.sort_by { |plan| plan[:level] }
  end

  # Get available downgrade options
  def available_downgrades
    return [] unless has_active_subscription?
    
    current_level = current_plan_level
    available_plans = []
    
    PLAN_HIERARCHY.each do |plan_id, level|
      if level < current_level && level > 0
        available_plans << plan_info(plan_id)
      end
    end
    
    available_plans.sort_by { |plan| plan[:level] }.reverse
  end

  private

  def valid_plan_id?(plan_id)
    PLAN_HIERARCHY.key?(plan_id)
  end

  def plan_type_from_id(plan_id)
    case plan_id
    when STANDARD_PLAN_ID
      'standard'
    when PREMIUM_PLAN_ID
      'premium'
    else
      'unknown'
    end
  end

  def determine_proration_behavior(new_plan_id)
    current_level = current_plan_level
    target_level = PLAN_HIERARCHY[new_plan_id] || 0
    
    if target_level > current_level
      # Upgrade: Create prorations (user pays difference immediately)
      'create_prorations'
    else
      # Downgrade: No prorations (change takes effect at next billing cycle)
      'none'
    end
  end
end
