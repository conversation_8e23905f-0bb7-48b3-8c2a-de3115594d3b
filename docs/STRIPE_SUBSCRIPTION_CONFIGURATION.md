# Stripe Subscription Configuration Guide

This guide explains how to configure Stripe subscription plans for seamless upgrades with proration in your SaaS application.

## Overview

Our subscription system ensures:
1. Users can only have one active subscription at a time
2. Seamless upgrades from Standard to Premium plans with proration
3. Proper handling of downgrades at next billing cycle
4. Integration with existing Pay gem and webhook system

## Stripe Dashboard Configuration

### 1. Product and Price Setup

#### Standard Plan
- **Price ID**: `price_1R9Q55DYYVPVcCCrWQOwsKmT`
- **Amount**: $29.00 USD
- **Billing**: Monthly recurring
- **Proration**: Enabled

#### Premium Plan
- **Price ID**: `price_1R9Q66DYYVPVcCCrnqiXNafF`
- **Amount**: $99.00 USD
- **Billing**: Monthly recurring
- **Proration**: Enabled

### 2. Customer Portal Configuration

In Stripe Dashboard → Settings → Customer Portal:

1. **Enable subscription management**
   - Allow customers to update subscriptions
   - Enable plan switching
   - Allow cancellation

2. **Configure proration settings**
   - Enable proration for subscription changes
   - Set proration behavior to "Create prorations"

3. **Allowed subscription updates**
   - Enable upgrade/downgrade between plans
   - Set upgrade behavior to "Immediate with proration"
   - Set downgrade behavior to "At period end"

## Code Implementation

### Core Components

1. **SubscriptionManager Service** (`app/services/subscription_manager.rb`)
   - Handles subscription logic and plan changes
   - Manages proration behavior
   - Ensures single subscription constraint

2. **Updated Controllers**
   - `Talent::SubscriptionsController` - Handles new subscriptions and upgrades
   - `Talent::Settings::SubscriptionsController` - Displays subscription management UI

3. **Enhanced Webhooks**
   - `Pay::Webhooks::Stripe::CustomerSubscriptionUpdated`
   - `Pay::Webhooks::Stripe::InvoicePaymentSucceeded`
   - `Pay::Webhooks::Stripe::CustomerSubscriptionDeleted`

### Key Features

#### Single Subscription Enforcement
```ruby
subscription_manager = SubscriptionManager.new(user)
subscription_manager.ensure_single_subscription!
```

#### Plan Change with Proration
```ruby
# Upgrade (immediate with proration)
subscription_manager.change_subscription(SubscriptionManager::PREMIUM_PLAN_ID)

# Downgrade (at next billing cycle)
subscription_manager.change_subscription(SubscriptionManager::STANDARD_PLAN_ID)
```

#### Plan Information and Validation
```ruby
# Check upgrade eligibility
subscription_manager.can_upgrade_to?(plan_id)

# Get available upgrade options
subscription_manager.available_upgrades

# Get plan details
subscription_manager.plan_info(plan_id)
```

## Proration Behavior

### Upgrades (Standard → Premium)
- **Timing**: Immediate
- **Proration**: User pays prorated difference immediately
- **Access**: Premium features available immediately
- **Billing**: Next billing date remains unchanged

### Downgrades (Premium → Standard)
- **Timing**: At next billing cycle
- **Proration**: No immediate charge/refund
- **Access**: Premium features remain until next billing cycle
- **Billing**: Lower amount charged at next billing cycle

## API Endpoints

### Create/Upgrade Subscription
```
POST /talent/subscription
PATCH /talent/subscription/upgrade
```

Parameters:
- `plan`: Stripe price ID

### View Subscription Management
```
GET /talent/settings/subscription
```

## Webhook Events

The system handles these Stripe webhook events:

1. **customer.subscription.updated**
   - Updates Pay::Subscription record
   - Updates user premium status
   - Ensures single subscription constraint

2. **invoice.payment_succeeded**
   - Confirms payment for subscription
   - Grants/revokes premium access based on plan

3. **customer.subscription.deleted**
   - Revokes premium access
   - Updates subscription status

## Testing

### Manual Testing Steps

1. **New Subscription**
   - User with no subscription creates Standard plan
   - Verify checkout flow and webhook processing
   - Confirm subscription appears in settings

2. **Upgrade Flow**
   - User with Standard plan upgrades to Premium
   - Verify immediate proration charge
   - Confirm premium features are enabled

3. **Downgrade Flow**
   - User with Premium plan downgrades to Standard
   - Verify no immediate charge
   - Confirm premium features remain until next billing cycle

### Automated Tests

Run the subscription manager tests:
```bash
rails test test/services/subscription_manager_test.rb
```

## Troubleshooting

### Common Issues

1. **Multiple Active Subscriptions**
   - The system automatically cancels duplicate subscriptions
   - Check logs for "multiple active subscriptions" warnings

2. **Webhook Processing Delays**
   - Subscription changes may take a few seconds to reflect
   - UI shows "Changes will be reflected shortly" message

3. **Proration Calculation**
   - Stripe handles all proration calculations
   - Check Stripe Dashboard for detailed proration invoices

### Monitoring

Monitor these logs for subscription-related events:
- `Pay::Webhooks::Stripe::CustomerSubscriptionUpdated`
- `Pay::Webhooks::Stripe::InvoicePaymentSucceeded`
- `Pay::Webhooks::Stripe::CustomerSubscriptionDeleted`

## Security Considerations

1. **Plan ID Validation**
   - All plan IDs are validated against allowed constants
   - Invalid plan IDs are rejected

2. **User Authorization**
   - Only authenticated users can modify their subscriptions
   - Users can only modify their own subscriptions

3. **Webhook Verification**
   - All webhooks are verified using Stripe signing secrets
   - Pay gem handles webhook signature validation

## Next Steps

1. **Test in Stripe Test Mode**
   - Use test price IDs for development
   - Verify all flows work correctly

2. **Production Deployment**
   - Update price IDs to production values
   - Configure production webhook endpoints

3. **Monitoring Setup**
   - Set up alerts for failed webhook processing
   - Monitor subscription metrics and proration amounts
